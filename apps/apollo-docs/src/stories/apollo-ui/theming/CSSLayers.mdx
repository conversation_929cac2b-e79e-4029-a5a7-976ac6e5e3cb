import { Meta } from "@storybook/addon-docs/blocks"


<Meta title="@apollo∕ui/Theming/CSS Layers" tags={["docs"]} />

# Layer

We use `@layer` to manage the order of CSS variables. There are 4 layers in Apollo:

## How Theme Layers Work

The theme styles are grouped into **four layers**:

<table>
  <thead>
    <tr>
      <th>Layer Name</th>
      <th>Purpose</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>reset</code></td>
      <td>Global reset styles for consistent rendering across browsers</td>
    </tr>
    <tr>
      <td><code>base</code></td>
      <td>Base typography, layout styles</td>
    </tr>
    <tr>
      <td><code>apollo</code></td>
      <td><strong>Theme version 2.0 (Apollo)</strong> – m3 design tokens, and components</td>
    </tr>
    <tr>
      <td><code>legacy</code></td>
      <td><strong>Theme (Legacy)</strong> – legacy tokens, and components</td>
    </tr>
  </tbody>
</table>

**Layer order matters** – later layers override earlier ones if styles conflict.

## Using Theme version 2.0 (Apollo)

By default, our styles are loaded in the following order for **Theme version 2.0**:

```css
@layer reset, base, legacy, apollo;
```

This means:

1. Browser resets (`reset`)
2. Base styles (`base`)
3. Theme (Legacy) styles (`legacy`)
4. Theme version 2.0 (Apollo) styles (`apollo`) _(final layer, takes precedence)_

## Using Theme (Legacy)

To use **Theme Version 1**, you need to include the `legacy` layer **after** `apollo`.
This ensures that legacy styles override Theme V2 where applicable.

**Example (Global CSS file):**

```css
@layer reset, base, apollo, legacy;
```

Order explanation:

1. Reset styles
2. Base styles
3. heme version 2.0 (Apollo) styles (for components that are unchanged)
4. **Theme (Legacy) overrides** (final layer, takes precedence)

## Mixing Themes (Not Recommended)

Technically, you can load both `apollo` and `legacy` layers, but this can lead to inconsistent styles.
If you must mix, **always place `legacy` last** so it overrides version 2.0 (Apollo) styles where needed.


## Quick Reference

<table>
  <thead>
    <tr>
      <th>Goal</th>
      <th>Layers to Include</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Use only Theme version 2.0 (Apollo)</td>
      <td><code>@layer reset, base, apollo;</code></td>
    </tr>
    <tr>
      <td>Use Theme (Legacy)</td>
      <td><code>@layer reset, base, apollo, legacy;</code></td>
    </tr>
    <tr>
      <td>Mix Legacy overrides on Apollo</td>
      <td><code>@layer reset, base, apollo, legacy;</code></td>
    </tr>
  </tbody>
</table>


## Notes

- **Order matters**: CSS from later layers always overrides earlier layers if selectors match.
- If you omit a layer from your `@layer` declaration, its styles will still load **but may have lower priority**.
- The `apollo` layer contains the most recent tokens and styles – it is the foundation for future updates.