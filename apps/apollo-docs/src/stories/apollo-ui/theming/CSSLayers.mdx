import { Meta } from "@storybook/addon-docs/blocks"


<Meta title="@apollo∕ui/Theming/CSS Layers" tags={["docs"]} />

# Layer

We use `@layer` to manage the order of CSS variables. There are 4 layers in Apollo:

## How Theme Layers Work

The theme styles are grouped into **four layers**:

| Layer Name | Purpose                                                          |
| ---------- | ---------------------------------------------------------------- |
| `reset`    | Global reset styles for consistent rendering across browsers     |
| `base`     | Base typography, layout styles                                   |
| `apollo`   | **Theme verion 2.0 (Apollo)** – m3 design tokens, and components |
| `legacy`   | **Theme (Legacy)** – legacy tokens, and components               |

**Layer order matters** – later layers override earlier ones if styles conflict.

## Using Theme version 2.0 (Apollo)

By default, our styles are loaded in the following order for **Theme version 2.0**:

```css
@layer reset, base, legacy, apollo;
```

This means:

1. Browser resets (`reset`)
2. Base styles (`base`)
3. Theme (Legacy) styles (`legacy`)
4. Theme version 2.0 (Apollo) styles (`apollo`) _(final layer, takes precedence)_

## Using Theme (Legacy)

To use **Theme Version 1**, you need to include the `legacy` layer **after** `apollo`.
This ensures that legacy styles override Theme V2 where applicable.

**Example (Global CSS file):**

```css
@layer reset, base, apollo, legacy;
```

Order explanation:

1. Reset styles
2. Base styles
3. heme version 2.0 (Apollo) styles (for components that are unchanged)
4. **Theme (Legacy) overrides** (final layer, takes precedence)

## Mixing Themes (Not Recommended)

Technically, you can load both `apollo` and `legacy` layers, but this can lead to inconsistent styles.
If you must mix, **always place `legacy` last** so it overrides version 2.0 (Apollo) styles where needed.


## Quick Reference

| Goal                                | Layers to Include                     |
| ----------------------------------- | ------------------------------------- |
| Use only Theme version 2.0 (Apollo) | `@layer reset, base, apollo;`         |
| Use Theme (Legacy)                  | `@layer reset, base, apollo, legacy;` |
| Mix Legacy overrides on Apollo      | `@layer reset, base, apollo, legacy;` |


## Notes

- **Order matters**: CSS from later layers always overrides earlier layers if selectors match.
- If you omit a layer from your `@layer` declaration, its styles will still load **but may have lower priority**.
- The `apollo` layer contains the most recent tokens and styles – it is the foundation for future updates.