import { Meta } from "@storybook/addon-docs/blocks"


<Meta title="@apollo∕ui/Theming/CSS Variables" tags={["docs"]} />

# CSS Variables

Apollo uses CSS variables to apply tokens. All CSS variables are prefixed with `apl` after transform from design tokens.


## Base CSS Variables

```tsx
const theme = {
  token: {
    base: {
      color: {
        primary: {
          40: "#f590a5", 
          50: "#f8b3cb",
        },
      },
    },
  },
}
```

CSS will be generated as following:

```css
:root {
  --apl-base-color-primary-40: #f590a5;
  --apl-base-color-primary-50: #f8b3cb;
}
```

## Alias CSS Variables

```tsx
const theme = {
  token: {
    base: {
      color: {
        primary: {
          40: "#f590a5", 
        },
      },
    },
    alias: {
      color: {
        primary: {
          primary: "{base.color.primary.40}", // Reference Global Token
        },
      },
    },
  },
}
```

CSS will be generated as following:

```css
:root {
  --apl-base-color-primary-40: #f590a5;
  --apl-alias-color-primary-primary: #f590a5;
}
```