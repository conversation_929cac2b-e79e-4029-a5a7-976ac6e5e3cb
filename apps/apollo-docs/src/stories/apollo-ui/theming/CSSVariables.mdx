import { Meta } from "@storybook/addon-docs/blocks"


<Meta title="@apollo∕ui/Theming/CSS Variables" tags={["docs"]} />

# CSS Variables

Apollo's theming system leverages CSS custom properties (variables) to provide dynamic, scalable styling. All CSS variables are automatically prefixed with `apl` during the design token transformation process.


<div style={{
  background: "linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "20px 0",
  border: "1px solid #bae6fd",
  borderLeft: "4px solid #0ea5e9"
}}>
  <h4 style={{
    margin: "0 0 12px 0",
    color: "#0c4a6e",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>🏗️</span> Token Transformation Pipeline
  </h4>
  <p style={{ margin: "0", color: "#0c4a6e" }}>
    Design tokens flow through a three-tier system: <strong>Global → Base → Alias</strong>, with each level generating corresponding CSS variables for maximum flexibility and maintainability.
  </p>
</div>


## Base CSS Variables

Base tokens build upon global tokens, providing semantic meaning and allowing for theme-specific customizations while maintaining references to the global foundation.

<div style={{
  background: "linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  border: "1px solid #60a5fa",
  borderLeft: "4px solid #3b82f6"
}}>
  <h4 style={{
    margin: "0 0 16px 0",
    color: "#1e40af",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>🔗</span> Base Token Definition
  </h4>

```tsx
const theme = {
  token: {
    base: {
      color: {
        primary: {
          40: "#f590a5", // Reference Global Token
          50: "#F8B3CB", // Modify
        },
      },
    },
  },
}
```

  <p style={{ margin: "16px 0 8px 0", color: "#1e40af", fontWeight: "500" }}>
    Generated CSS Variables:
  </p>

```css
:root {
  --apl-base-color-primary-40: #f590a5;
  --apl-base-color-primary-50: #f8b3cb;
}
```

</div>

## Alias CSS Variables

Alias tokens provide the final layer of abstraction, creating semantic names that components can use. They reference base tokens and provide the most specific, component-ready variable names.

<div style={{
  background: "linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  border: "1px solid #86efac",
  borderLeft: "4px solid #22c55e"
}}>
  <h4 style={{
    margin: "0 0 16px 0",
    color: "#166534",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>🏷️</span> Alias Token Definition
  </h4>

```tsx
const theme = {
  token: {
    base: {
      color: {
        primary: {
          40: "#f590a5",
        },
      },
    },
    alias: {
      color: {
        primary: {
          primary: "{base.color.primary.40}", // Reference Base Token
          "on-primary": "#f8b3cb",
        },
      },
    },
  },
}
```

  <p style={{ margin: "16px 0 8px 0", color: "#166534", fontWeight: "500" }}>
    Generated CSS Variables:
  </p>

```css
:root {
  --apl-base-color-primary-40: #f590a5;
  --apl-alias-color-primary-primary: #f590a5;
  --apl-alias-color-primary-on-primary: #f8b3cb;
}
```

</div>

## Variable Naming Convention

<div style={{
  background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
  borderRadius: "12px",
  padding: "24px",
  margin: "20px 0",
  border: "1px solid #cbd5e1"
}}>
  <table style={{
    width: "100%",
    borderCollapse: "collapse",
    background: "white",
    borderRadius: "8px",
    overflow: "hidden",
    boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
  }}>
    <thead>
      <tr style={{
        background: "linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)",
        color: "white"
      }}>
        <th style={{
          padding: "16px 20px",
          textAlign: "left",
          fontWeight: "600",
          border: "none"
        }}>Token Type</th>
        <th style={{
          padding: "16px 20px",
          textAlign: "left",
          fontWeight: "600",
          border: "none"
        }}>CSS Variable Pattern</th>
        <th style={{
          padding: "16px 20px",
          textAlign: "left",
          fontWeight: "600",
          border: "none"
        }}>Example</th>
      </tr>
    </thead>
    <tbody>
      <tr style={{ borderBottom: "1px solid #f1f5f9" }}>
        <td style={{
          padding: "16px 20px",
          border: "none",
          fontWeight: "500"
        }}>
          <span style={{
            background: "#dbeafe",
            color: "#1e40af",
            padding: "4px 8px",
            borderRadius: "4px",
            fontSize: "14px"
          }}>Base</span>
        </td>
        <td style={{ padding: "16px 20px", border: "none", color: "#475569" }}>
          <code style={{
            background: "#f1f5f9",
            padding: "4px 8px",
            borderRadius: "4px",
            color: "#475569"
          }}>--apl-base-{`{path}`}</code>
        </td>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#dbeafe",
            padding: "4px 8px",
            borderRadius: "4px",
            color: "#1e40af",
            fontSize: "13px"
          }}>--apl-base-color-primary-40</code>
        </td>
      </tr>
      <tr>
        <td style={{
          padding: "16px 20px",
          border: "none",
          fontWeight: "500"
        }}>
          <span style={{
            background: "#dcfce7",
            color: "#166534",
            padding: "4px 8px",
            borderRadius: "4px",
            fontSize: "14px"
          }}>Alias</span>
        </td>
        <td style={{ padding: "16px 20px", border: "none", color: "#475569" }}>
          <code style={{
            background: "#f1f5f9",
            padding: "4px 8px",
            borderRadius: "4px",
            color: "#475569"
          }}>--apl-alias-{`{path}`}</code>
        </td>
        <td style={{ padding: "16px 20px", border: "none" }}>
          <code style={{
            background: "#dcfce7",
            padding: "4px 8px",
            borderRadius: "4px",
            color: "#166534",
            fontSize: "13px"
          }}>--apl-alias-color-primary-primary</code>
        </td>
      </tr>
    </tbody>
  </table>
</div>

## 🚀 Usage in Components

<div style={{
  background: "linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%)",
  borderRadius: "12px",
  padding: "20px",
  margin: "16px 0",
  border: "1px solid #d8b4fe",
  borderLeft: "4px solid #a855f7"
}}>
  <h4 style={{
    margin: "0 0 16px 0",
    color: "#7c2d12",
    display: "flex",
    alignItems: "center"
  }}>
    <span style={{ marginRight: "8px" }}>⚡</span> Using CSS Variables in Components
  </h4>

```css
.my-component {
  /* Use alias tokens for semantic styling */
  background-color: var(--apl-alias-color-primary-primary);

  /* Use base tokens for more specific control */
  border-color: var(--apl-base-color-primary-40);
}
```

  <p style={{ margin: "16px 0 0 0", color: "#7c2d12" }}>
    <strong>💡 Pro Tip:</strong> Prefer alias tokens in components for better maintainability and semantic meaning.
  </p>
</div>

## 💡 Best Practices

<div style={{
  background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
  borderRadius: "12px",
  padding: "24px",
  margin: "20px 0",
  border: "1px solid #cbd5e1"
}}>
  <div style={{ display: "grid", gap: "16px" }}>

    <div style={{
      background: "white",
      padding: "16px",
      borderRadius: "8px",
      borderLeft: "4px solid #10b981"
    }}>
      <h4 style={{
        margin: "0 0 8px 0",
        color: "#059669",
        display: "flex",
        alignItems: "center"
      }}>
        <span style={{ marginRight: "8px" }}>✅</span> Do: Use Semantic Tokens
      </h4>
      <p style={{ margin: "0", color: "#475569" }}>
        Prefer alias tokens like <code style={{
          background: "#f1f5f9",
          padding: "2px 6px",
          borderRadius: "3px"
        }}>--apl-alias-color-primary-primary</code> for component styling as they provide semantic meaning and are easier to maintain.
      </p>
    </div>

    <div style={{
      background: "white",
      padding: "16px",
      borderRadius: "8px",
      borderLeft: "4px solid #3b82f6"
    }}>
      <h4 style={{
        margin: "0 0 8px 0",
        color: "#1e40af",
        display: "flex",
        alignItems: "center"
      }}>
        <span style={{ marginRight: "8px" }}>🎯</span> Consistent Naming
      </h4>
      <p style={{ margin: "0", color: "#475569" }}>
        Follow the established naming convention: <code style={{
          background: "#f1f5f9",
          padding: "2px 6px",
          borderRadius: "3px"
        }}>--apl-[type]-[category]-[property]-[variant]</code> for predictable variable names.
      </p>
    </div>

    <div style={{
      background: "white",
      padding: "16px",
      borderRadius: "8px",
      borderLeft: "4px solid #f59e0b"
    }}>
      <h4 style={{
        margin: "0 0 8px 0",
        color: "#d97706",
        display: "flex",
        alignItems: "center"
      }}>
        <span style={{ marginRight: "8px" }}>⚠️</span> Fallback Values
      </h4>
      <p style={{ margin: "0", color: "#475569" }}>
        Always provide fallback values when using CSS variables: <code style={{
          background: "#f1f5f9",
          padding: "2px 6px",
          borderRadius: "3px"
        }}>var(--apl-color-primary, #fallback)</code> for better browser compatibility.
      </p>
    </div>

  </div>
</div>

---

<div style={{
  textAlign: "center",
  padding: "20px",
  color: "#64748b",
  fontStyle: "italic"
}}>
  <p style={{ margin: "0" }}>
    For more information about CSS Custom Properties, visit the
    <a
      href="https://developer.mozilla.org/en-US/docs/Web/CSS/--*"
      target="_blank"
      style={{ color: "#3b82f6", textDecoration: "none" }}
    >
      MDN documentation
    </a>
  </p>
</div>